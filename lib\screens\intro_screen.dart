import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math' as math;
import '../theme/simple_theme.dart';
import 'splash_screen.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _backgroundController;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final List<IntroPageData> _pages = [
    IntroPageData(
      title: '🎓 أهلاً وسهلاً في EduTrack',
      subtitle: 'رحلتك التعليمية الذكية تبدأ من هنا',
      description:
          'منصة إدارة التعليم الأكثر تطوراً هتساعدك تنظم طلابك ومجموعاتك بأحدث التقنيات الذكية',
      icon: Icons.school_rounded,
      color: SimpleTheme.primaryBlue,
      gradient: [SimpleTheme.primaryBlue, SimpleTheme.primaryBlueDark],
      features: [
        '🤖 ذكاء اصطناعي متطور',
        '📈 تتبع تقدم فوري',
        '📊 تقارير تفاعلية ذكية',
      ],
    ),
    IntroPageData(
      title: '👥 إدارة المجموعات الذكية',
      subtitle: 'نظم طلابك بأحدث التقنيات',
      description:
          'اعمل مجموعات ديناميكية للطلاب مع نظام تتبع ذكي يحلل الأداء ويقترح التحسينات تلقائياً',
      icon: Icons.groups_rounded,
      color: SimpleTheme.primaryPurple,
      gradient: [SimpleTheme.primaryPurple, SimpleTheme.primaryPurpleLight],
      features: [
        '🎯 تجميع ذكي تلقائي',
        '📱 متابعة لحظية',
        '🔍 تحليل سلوك الطلاب',
      ],
    ),
    IntroPageData(
      title: '📅 جدولة متطورة ومرنة',
      subtitle: 'خطط دروسك بذكاء وإبداع',
      description:
          'نظام جدولة متقدم مع تذكيرات ذكية وتحليل أوقات الذروة لتحسين الأداء التعليمي',
      icon: Icons.calendar_today_rounded,
      color: SimpleTheme.accentPink,
      gradient: [SimpleTheme.accentPink, SimpleTheme.accentPinkLight],
      features: [
        '⏰ جدولة تلقائية ذكية',
        '🔔 تنبيهات مخصصة',
        '📊 تحليل أوقات الذروة',
      ],
    ),
    IntroPageData(
      title: '📊 لوحة تحكم تفاعلية',
      subtitle: 'تحليلات متقدمة ورؤى عميقة',
      description:
          'احصل على رؤى عميقة وتحليلات متطورة لأداء طلابك مع توقعات ذكية للنتائج المستقبلية',
      icon: Icons.dashboard_rounded,
      color: SimpleTheme.success,
      gradient: [SimpleTheme.success, SimpleTheme.successLight],
      features: [
        '🧠 تحليلات بالذكاء الاصطناعي',
        '📈 توقعات مستقبلية',
        '🎨 تصورات بيانات تفاعلية',
      ],
    ),
    IntroPageData(
      title: '🚀 انطلق نحو المستقبل',
      subtitle: 'كل شيء جاهز للإبداع!',
      description:
          'الآن أصبح بإمكانك الاستفادة من أقوى منصة تعليمية ذكية مع تجربة مستخدم استثنائية ومميزات لا محدودة',
      icon: Icons.rocket_launch_rounded,
      color: SimpleTheme.warning,
      gradient: [SimpleTheme.warning, SimpleTheme.warningLight],
      features: [
        '✨ واجهة عصرية وسهلة',
        '⚡ أداء فائق السرعة',
        '🛡️ أمان وحماية متقدمة',
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();

    // Background animation controller
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );

    // Slide animation controller
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Fade animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Initialize animations
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeInOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // Start animations
    _backgroundController.repeat();
    _fadeController.forward();

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _backgroundController.dispose();
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });

    // Add haptic feedback on page change
    HapticFeedback.selectionClick();

    // Restart slide animation
    _slideController.reset();
    _slideController.forward();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      HapticFeedback.lightImpact();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOutCubic,
      );
    } else {
      _startApp();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      HapticFeedback.lightImpact();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOutCubic,
      );
    }
  }

  void _startApp() async {
    HapticFeedback.mediumImpact();

    // حفظ أن المستخدم قد رأى شاشة الترحيب
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenIntro', true);

    // الانتقال إلى الشاشة التالية
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const SplashScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position:
                    Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOutCubic,
                      ),
                    ),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _pages[_currentPage].gradient[0],
                _pages[_currentPage].gradient[1],
                SimpleTheme.darkBg,
              ],
              stops: const [0.0, 0.6, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // Enhanced animated background
              AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  return Positioned.fill(
                    child: CustomPaint(
                      painter: ModernIntroBgPainter(
                        _backgroundController.value,
                        _pages[_currentPage].color,
                      ),
                    ),
                  );
                },
              ),

              // Floating particles
              AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  return Positioned.fill(
                    child: CustomPaint(
                      painter: ParticlesPainter(
                        _backgroundController.value,
                        _pages[_currentPage].color,
                      ),
                    ),
                  );
                },
              ),

              // Main content
              SafeArea(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    children: [
                      // Top navigation
                      _buildTopNavigation(),

                      // Page content
                      Expanded(
                        child: PageView.builder(
                          controller: _pageController,
                          onPageChanged: _onPageChanged,
                          itemCount: _pages.length,
                          itemBuilder: (context, index) {
                            return AnimatedBuilder(
                              animation: _slideAnimation,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(
                                    _slideAnimation.value * size.width * 0.3,
                                    0,
                                  ),
                                  child: Opacity(
                                    opacity: 1.0 - _slideAnimation.value,
                                    child: _buildModernPage(
                                      _pages[index],
                                      index,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),

                      // Bottom navigation
                      _buildBottomNavigation(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          if (_currentPage > 0)
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: IconButton(
                onPressed: _previousPage,
                icon: const Icon(
                  Icons.arrow_back_ios_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            )
          else
            const SizedBox(width: 48),

          // Progress indicator
          Text(
            '${_currentPage + 1} من ${_pages.length}',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),

          // Skip button
          if (_currentPage < _pages.length - 1)
            TextButton(
              onPressed: _startApp,
              style: TextButton.styleFrom(
                backgroundColor: Colors.white.withValues(alpha: 0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              child: Text(
                'تخطي',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildModernPage(IntroPageData page, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Enhanced animated icon container with pulsing effect
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1500),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  final pulseValue =
                      0.95 +
                      (0.05 *
                          math.sin(_backgroundController.value * math.pi * 4));
                  return Transform.scale(
                    scale: (0.8 + (value * 0.2)) * pulseValue,
                    child: Container(
                      width: 160,
                      height: 160,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          center: Alignment.topLeft,
                          radius: 1.2,
                          colors: [
                            page.gradient[0],
                            page.gradient[1],
                            page.gradient[0].withValues(alpha: 0.8),
                          ],
                          stops: const [0.0, 0.7, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: page.color.withValues(alpha: 0.5),
                            blurRadius: 40,
                            spreadRadius: 8,
                            offset: const Offset(0, 15),
                          ),
                          BoxShadow(
                            color: page.color.withValues(alpha: 0.3),
                            blurRadius: 80,
                            spreadRadius: 15,
                            offset: const Offset(0, 25),
                          ),
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.1),
                            blurRadius: 20,
                            spreadRadius: -5,
                            offset: const Offset(-10, -10),
                          ),
                        ],
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 2,
                          ),
                        ),
                        child: Icon(page.icon, size: 80, color: Colors.white),
                      ),
                    ),
                  );
                },
              );
            },
          ),
          const SizedBox(height: 50),

          // Enhanced title with gradient text effect
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 40 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: ShaderMask(
                    shaderCallback: (bounds) => LinearGradient(
                      colors: [
                        Colors.white,
                        page.color.withValues(alpha: 0.8),
                        Colors.white,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ).createShader(bounds),
                    child: Text(
                      page.title,
                      style: GoogleFonts.cairo(
                        fontSize: 34,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                        height: 1.1,
                        letterSpacing: -0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 12),

          // Enhanced subtitle with glow effect
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1200),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 25 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: page.color.withValues(alpha: 0.1),
                      border: Border.all(
                        color: page.color.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: page.color.withValues(alpha: 0.2),
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Text(
                      page.subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: page.color,
                        height: 1.2,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),

          // Description
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1200),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 15 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: Text(
                    page.description,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                      height: 1.6,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 40),

          // Features list
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1400),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 10 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: _buildFeaturesList(page.features, page.color),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList(List<String> features, Color color) {
    return Column(
      children: features.asMap().entries.map((entry) {
        final index = entry.key;
        final feature = entry.value;

        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 600 + (index * 200)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(30 * (1 - value), 0),
              child: Opacity(
                opacity: value,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: color.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check_circle_rounded, color: color, size: 20),
                      const SizedBox(width: 12),
                      Text(
                        feature,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Page indicators
          Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              _pages.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: _currentPage == index ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: _currentPage == index
                      ? _pages[_currentPage].color
                      : Colors.white.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),

          // Enhanced Next/Start button with hover effect
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 300),
            tween: Tween(begin: 1.0, end: 1.0),
            builder: (context, value, child) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _pages[_currentPage].gradient[0],
                      _pages[_currentPage].gradient[1],
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: _pages[_currentPage].color.withValues(alpha: 0.5),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                      spreadRadius: 2,
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(-5, -5),
                      spreadRadius: -2,
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _nextPage,
                    borderRadius: BorderRadius.circular(20),
                    splashColor: Colors.white.withValues(alpha: 0.2),
                    highlightColor: Colors.white.withValues(alpha: 0.1),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 28,
                        vertical: 18,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _currentPage < _pages.length - 1
                                ? 'التالي'
                                : 'ابدأ الآن',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.w800,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(width: 12),
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: Icon(
                              _currentPage < _pages.length - 1
                                  ? Icons.arrow_forward_rounded
                                  : Icons.rocket_launch_rounded,
                              key: ValueKey(_currentPage < _pages.length - 1),
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

// Data class for intro pages
class IntroPageData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;
  final List<Color> gradient;
  final List<String> features;

  IntroPageData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
    required this.gradient,
    required this.features,
  });
}

// Modern background painter
class ModernIntroBgPainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;

  ModernIntroBgPainter(this.animationValue, this.primaryColor);

  @override
  void paint(Canvas canvas, Size size) {
    // Create flowing wave patterns
    for (int i = 0; i < 6; i++) {
      final paint = Paint()
        ..shader =
            RadialGradient(
              colors: [
                primaryColor.withValues(alpha: 0.15 - (i * 0.02)),
                primaryColor.withValues(alpha: 0.05 - (i * 0.01)),
                Colors.transparent,
              ],
              stops: const [0.0, 0.7, 1.0],
            ).createShader(
              Rect.fromCircle(
                center: _getWavePosition(i, size),
                radius: _getWaveRadius(i, size),
              ),
            );

      canvas.drawCircle(
        _getWavePosition(i, size),
        _getWaveRadius(i, size),
        paint,
      );
    }
  }

  Offset _getWavePosition(int index, Size size) {
    final baseX = size.width * (0.1 + (index % 3) * 0.4);
    final baseY = size.height * (0.1 + (index ~/ 3) * 0.4);

    final offsetX = 80 * math.sin(animationValue * math.pi * 2 + index * 0.8);
    final offsetY = 60 * math.cos(animationValue * math.pi * 2 + index * 0.6);

    return Offset(
      (baseX + offsetX).clamp(0, size.width),
      (baseY + offsetY).clamp(0, size.height),
    );
  }

  double _getWaveRadius(int index, Size size) {
    final baseRadius = size.width * (0.15 + index * 0.05);
    final pulseFactor =
        0.3 * math.sin(animationValue * math.pi * 2 + index * 0.4) + 1;
    return baseRadius * pulseFactor;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Floating particles painter
class ParticlesPainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;

  ParticlesPainter(this.animationValue, this.primaryColor);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw floating particles
    for (int i = 0; i < 20; i++) {
      final paint = Paint()
        ..color = primaryColor.withValues(alpha: 0.1 + (i % 3) * 0.05)
        ..style = PaintingStyle.fill;

      final position = _getParticlePosition(i, size);
      final radius = _getParticleRadius(i);

      canvas.drawCircle(position, radius, paint);
    }
  }

  Offset _getParticlePosition(int index, Size size) {
    final baseX = size.width * (index % 5) / 5;
    final baseY = size.height * (index % 4) / 4;

    final speed = 0.5 + (index % 3) * 0.3;
    final offsetX = 50 * math.sin(animationValue * math.pi * 2 * speed + index);
    final offsetY =
        30 * math.cos(animationValue * math.pi * 2 * speed + index * 0.7);

    return Offset(
      (baseX + offsetX).clamp(0, size.width),
      (baseY + offsetY).clamp(0, size.height),
    );
  }

  double _getParticleRadius(int index) {
    final baseRadius = 2.0 + (index % 4);
    final pulseFactor =
        0.5 * math.sin(animationValue * math.pi * 4 + index) + 1;
    return baseRadius * pulseFactor;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

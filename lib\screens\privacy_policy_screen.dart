import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'سياسة الخصوصية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('مقدمة'),
            _buildParagraph(
              'نحن في تطبيق EduTrack نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. '
              'تشرح سياسة الخصوصية هذه كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
            ),
            
            _buildSectionHeader('البيانات التي نجمعها'),
            _buildParagraph(
              'يقوم تطبيق EduTrack بتخزين جميع البيانات محلياً على جهازك فقط، ولا يتم إرسال أي بيانات إلى خوادم خارجية. '
              'البيانات المخزنة تشمل:',
            ),
            _buildBulletPoint('معلومات الطلاب والمجموعات التعليمية'),
            _buildBulletPoint('سجلات الحضور والغياب'),
            _buildBulletPoint('جداول الدروس والمواعيد'),
            _buildBulletPoint('سجلات المدفوعات'),
            _buildBulletPoint('إعدادات التطبيق وتفضيلات المستخدم'),
            
            _buildSectionHeader('كيفية استخدام البيانات'),
            _buildParagraph(
              'نستخدم البيانات التي تقوم بإدخالها في التطبيق فقط لتوفير الخدمات التالية:',
            ),
            _buildBulletPoint('إدارة المجموعات والطلاب'),
            _buildBulletPoint('تتبع الحضور والغياب'),
            _buildBulletPoint('إدارة الجداول الدراسية'),
            _buildBulletPoint('متابعة المدفوعات'),
            _buildBulletPoint('إنشاء النسخ الاحتياطية واستعادتها'),
            
            _buildSectionHeader('حماية البيانات'),
            _buildParagraph(
              'نحن نتخذ الإجراءات التالية لحماية بياناتك:',
            ),
            _buildBulletPoint('تخزين جميع البيانات محلياً على جهازك فقط'),
            _buildBulletPoint('عدم مشاركة أي بيانات مع أطراف ثالثة'),
            _buildBulletPoint('تشفير النسخ الاحتياطية لحماية البيانات'),
            _buildBulletPoint('عدم جمع أي معلومات تعريف شخصية دون موافقتك'),
            
            _buildSectionHeader('التحديثات والاتصال بالإنترنت'),
            _buildParagraph(
              'يستخدم التطبيق الاتصال بالإنترنت فقط للتحقق من وجود تحديثات جديدة. '
              'لا يتم إرسال أي بيانات شخصية أو معلومات عن استخدامك للتطبيق أثناء هذه العملية.',
            ),
            
            _buildSectionHeader('حقوقك'),
            _buildParagraph(
              'لديك الحق الكامل في:',
            ),
            _buildBulletPoint('الوصول إلى بياناتك المخزنة في التطبيق'),
            _buildBulletPoint('تصحيح أي معلومات غير دقيقة'),
            _buildBulletPoint('حذف بياناتك من التطبيق في أي وقت'),
            _buildBulletPoint('نقل بياناتك عبر ميزة النسخ الاحتياطي'),
            
            _buildSectionHeader('التغييرات على سياسة الخصوصية'),
            _buildParagraph(
              'قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. سيتم إعلامك بأي تغييرات مهمة '
              'من خلال إشعار داخل التطبيق أو عند تثبيت تحديث جديد.',
            ),
            
            _buildSectionHeader('اتصل بنا'),
            _buildParagraph(
              'إذا كان لديك أي أسئلة أو استفسارات حول سياسة الخصوصية، يرجى التواصل معنا على:',
            ),
            _buildParagraph('البريد الإلكتروني: <EMAIL>'),
            
            const SizedBox(height: 30),
            Center(
              child: Text(
                'آخر تحديث: يونيو 2024',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 8),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF6366f1),
        ),
      ),
    );
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.justify,
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 16, bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.cairo(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}
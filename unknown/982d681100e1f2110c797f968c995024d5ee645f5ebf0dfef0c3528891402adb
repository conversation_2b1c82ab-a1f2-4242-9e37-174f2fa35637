import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';

import 'enhanced_card.dart';
import 'animated_counter.dart';

class StatsDashboard extends StatefulWidget {
  final List<StatItem> stats;
  final int crossAxisCount;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final double spacing;

  const StatsDashboard({
    super.key,
    required this.stats,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.1,
    this.padding,
    this.spacing = 16,
  });

  @override
  State<StatsDashboard> createState() => _StatsDashboardState();
}

class _StatsDashboardState extends State<StatsDashboard>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.stats.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 200)),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.elasticOut));
    }).toList();

    // Start animations with staggered delay
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (!_isDisposed && mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.zero,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          crossAxisSpacing: widget.spacing,
          mainAxisSpacing: widget.spacing,
          childAspectRatio: widget.childAspectRatio,
        ),
        itemCount: widget.stats.length,
        itemBuilder: (context, index) {
          final stat = widget.stats[index];
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Transform.scale(
                scale: _animations[index].value,
                child: Transform.translate(
                  offset: Offset(0, (1 - _animations[index].value) * 50),
                  child: Opacity(
                    opacity: (_animations[index].value).clamp(0.0, 1.0),
                    child: _buildStatCard(stat, index),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildStatCard(StatItem stat, int index) {
    return EnhancedCard(
      onTap: stat.onTap,
      type: CardType.premium,
      enableHoverEffect: true,
      enablePressEffect: true,
      padding: const EdgeInsets.all(16),
      animationDuration: const Duration(milliseconds: 250),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Enhanced Icon Container
          Hero(
            tag: 'stat_icon_$index',
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    stat.color.withValues(alpha: 0.2),
                    stat.color.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: stat.color.withValues(alpha: 0.4),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: stat.color.withValues(alpha: 0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: -2,
                  ),
                ],
              ),
              child: Icon(stat.icon, color: stat.color, size: 28),
            ),
          ),
          const SizedBox(height: 12),
          // Enhanced Counter
          Hero(
            tag: 'stat_value_$index',
            child: Material(
              color: Colors.transparent,
              child: ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [SimpleTheme.textPrimary, SimpleTheme.textSecondary],
                ).createShader(bounds),
                child: AnimatedCounter(
                  value: stat.value,
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                    letterSpacing: 1,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Enhanced Title
          Text(
            stat.title,
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(
              fontSize: 15,
              color: SimpleTheme.textSecondary,
              fontWeight: FontWeight.w700,
              letterSpacing: 0.3,
            ),
          ),
          const SizedBox(height: 8),
          // Progress Indicator
          if (stat.progress != null) ...[
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: (stat.progress! / 100).clamp(0.0, 1.0),
              backgroundColor: stat.color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(stat.color),
              borderRadius: BorderRadius.circular(2),
            ),
            const SizedBox(height: 4),
            Text(
              '${stat.progress!.isNaN ? 0 : stat.progress!.toInt()}%',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: SimpleTheme.textMuted,
                fontWeight: FontWeight.w600,
              ),
            ),
          ] else ...[
            // Subtle indicator
            Container(
              width: 30,
              height: 3,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    stat.color.withValues(alpha: 0.6),
                    stat.color.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class StatItem {
  final String title;
  final int value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final double? progress;
  final String? subtitle;

  const StatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.progress,
    this.subtitle,
  });
}

// Quick Stats Widget for common use cases
class QuickStats extends StatelessWidget {
  final int totalGroups;
  final int totalStudents;
  final int completedLessons;
  final int remainingLessons;
  final VoidCallback? onGroupsTap;
  final VoidCallback? onStudentsTap;
  final VoidCallback? onCompletedTap;
  final VoidCallback? onRemainingTap;

  const QuickStats({
    super.key,
    required this.totalGroups,
    required this.totalStudents,
    required this.completedLessons,
    required this.remainingLessons,
    this.onGroupsTap,
    this.onStudentsTap,
    this.onCompletedTap,
    this.onRemainingTap,
  });

  @override
  Widget build(BuildContext context) {
    final stats = [
      StatItem(
        title: 'المجموعات',
        value: totalGroups,
        icon: Icons.groups_rounded,
        color: SimpleTheme.primary,
        onTap: onGroupsTap,
      ),
      StatItem(
        title: 'الطلاب',
        value: totalStudents,
        icon: Icons.person_rounded,
        color: SimpleTheme.accentPink,
        onTap: onStudentsTap,
      ),
      StatItem(
        title: 'دروس مكتملة',
        value: completedLessons,
        icon: Icons.check_circle_rounded,
        color: SimpleTheme.success,
        onTap: onCompletedTap,
        progress: () {
          final total = completedLessons + remainingLessons;
          if (total <= 0) return 0.0;
          final percentage = (completedLessons / total) * 100;
          return percentage.isNaN || percentage.isInfinite ? 0.0 : percentage;
        }(),
      ),
      StatItem(
        title: 'دروس متبقية',
        value: remainingLessons,
        icon: Icons.schedule_rounded,
        color: SimpleTheme.warning,
        onTap: onRemainingTap,
      ),
    ];

    return StatsDashboard(stats: stats);
  }
}

import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import '../services/data_service.dart';

class AppProvider extends ChangeNotifier {
  bool _isOnline = false;
  List<Student> _students = [];
  List<Group> _groups = [];
  List<Lesson> _lessons = [];

  // Theme Management
  bool _isDarkMode = true;
  ThemeMode _themeMode = ThemeMode.dark;

  // Navigation Bar Position
  bool _isNavBarAtTop = false;

  // Theme Customization
  final String _selectedTheme = 'أزرق كلاسيكي';
  final String _primaryColor = 'أزرق';
  final String _fontFamily = 'القاهرة';
  final String _fontSize = 'متوسط';
  final bool _autoNightMode = false;
  final TimeOfDay _nightModeStart = const TimeOfDay(hour: 20, minute: 0);
  final TimeOfDay _nightModeEnd = const TimeOfDay(hour: 6, minute: 0);

  bool get isOnline => _isOnline;
  List<Student> get students => _students;
  List<Group> get groups => _groups;
  List<Lesson> get lessons => _lessons;

  // Theme Getters
  bool get isDarkMode => _isDarkMode;
  ThemeMode get themeMode => _themeMode;

  // Navigation Bar Position Getter
  bool get isNavBarAtTop => _isNavBarAtTop;

  // Theme Customization Getters
  String get selectedTheme => _selectedTheme;
  String get primaryColor => _primaryColor;
  String get fontFamily => _fontFamily;
  String get fontSize => _fontSize;
  bool get autoNightMode => _autoNightMode;
  TimeOfDay get nightModeStart => _nightModeStart;
  TimeOfDay get nightModeEnd => _nightModeEnd;

  AppProvider() {
    _initConnectivity();
    _loadData(); // تحميل البيانات مباشرة
    _loadThemeSettings();
  }

  // تم استبدال هذه الدالة باستدعاء مباشر لـ _loadData في المنشئ

  void _initConnectivity() async {
    final connectivity = Connectivity();
    final result = await connectivity.checkConnectivity();
    _isOnline = result != ConnectivityResult.none;

    connectivity.onConnectivityChanged.listen((result) {
      _isOnline = result != ConnectivityResult.none;
      notifyListeners();
    });
  }

  void loadData() {
    _students = DataService.students.values.toList();
    _groups = DataService.groups.values.toList();
    _lessons = DataService.lessons.values.toList();
    notifyListeners();
  }

  // للاستخدام الداخلي
  void _loadData() {
    loadData();
  }

  Future<void> addStudent(Student student) async {
    await DataService.addStudent(student);
    _loadData();
  }

  Future<void> addGroup(Group group) async {
    await DataService.addGroup(group);
    _loadData();
  }

  Future<void> addLesson(Lesson lesson) async {
    await DataService.addLesson(lesson);
    _loadData();
  }

  Future<void> updateStudent(Student student) async {
    await DataService.updateStudent(student);
    _loadData();
  }

  Future<void> updateGroup(Group group) async {
    await DataService.updateGroup(group);
    _loadData();
  }

  Future<void> updateLesson(Lesson lesson) async {
    await DataService.updateLesson(lesson);
    _loadData();
  }

  Future<void> deleteStudent(String id) async {
    await DataService.deleteStudent(id);
    _loadData();
  }

  Future<void> deleteGroup(String id) async {
    await DataService.deleteGroup(id);
    _loadData();
  }

  Future<void> deleteLesson(String id) async {
    await DataService.deleteLesson(id);
    _loadData();
  }

  List<Student> getStudentsByGroup(String groupId) {
    return _students.where((s) => s.groupId == groupId).toList();
  }

  List<Lesson> getTodayLessons() {
    final today = DateTime.now();
    return _lessons
        .where(
          (l) =>
              l.dateTime.year == today.year &&
              l.dateTime.month == today.month &&
              l.dateTime.day == today.day,
        )
        .toList();
  }

  List<Lesson> getTomorrowLessons() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return _lessons
        .where(
          (l) =>
              l.dateTime.year == tomorrow.year &&
              l.dateTime.month == tomorrow.month &&
              l.dateTime.day == tomorrow.day,
        )
        .toList();
  }

  int get totalStudents => _students.length;
  int get totalGroups => _groups.length;
  int get completedLessonsToday =>
      getTodayLessons().where((l) => l.isCompleted).length;
  int get remainingLessonsToday =>
      getTodayLessons().where((l) => !l.isCompleted).length;

  // Theme Management Methods
  Future<void> _loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool('isDarkMode') ?? true;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
      _isNavBarAtTop = prefs.getBool('isNavBarAtTop') ?? false;
      notifyListeners();
    } catch (e) {
      // استخدام القيم الافتراضية في حالة الخطأ
      _isDarkMode = true;
      _themeMode = ThemeMode.dark;
      _isNavBarAtTop = false;
    }
  }

  Future<void> toggleTheme() async {
    try {
      _isDarkMode = !_isDarkMode;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، إرجاع القيمة السابقة
      _isDarkMode = !_isDarkMode;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    try {
      _themeMode = mode;
      _isDarkMode = mode == ThemeMode.dark;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، الاحتفاظ بالقيم الحالية
    }
  }

  // Navigation Bar Position Management
  Future<void> toggleNavBarPosition() async {
    try {
      _isNavBarAtTop = !_isNavBarAtTop;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isNavBarAtTop', _isNavBarAtTop);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، إرجاع القيمة السابقة
      _isNavBarAtTop = !_isNavBarAtTop;
    }
  }

  Future<void> setNavBarPosition(bool isAtTop) async {
    try {
      _isNavBarAtTop = isAtTop;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isNavBarAtTop', _isNavBarAtTop);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، الاحتفاظ بالقيم الحالية
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';

import '../services/backup_service.dart';
import '../services/data_service.dart';
import 'backup_manager_screen.dart';
import 'check_updates_screen.dart';
import 'privacy_policy_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final isDark = provider.isDarkMode;
        return Container(
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF121212) : const Color(0xFFFAFAFA),
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  _buildModernHeader(isDark),
                  const SizedBox(height: 32),

                  // Settings Content
                  _buildModernAppSettings(context, provider, isDark),
                  const SizedBox(height: 24),
                  _buildModernDataSettings(context, provider, isDark),
                  const SizedBox(height: 24),
                  _buildModernBackupSettings(context, provider, isDark),
                  const SizedBox(height: 24),
                  _buildModernAboutSettings(context, isDark),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernHeader(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: isDark
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.03),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366F1), Color(0xFF8B8CF8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.settings_rounded,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الإعدادات',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إدارة إعدادات التطبيق والبيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: isDark
                        ? Colors.white.withValues(alpha: 0.7)
                        : const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernAppSettings(
    BuildContext context,
    AppProvider provider,
    bool isDark,
  ) {
    return _buildModernSettingsSection(
      title: 'إعدادات التطبيق',
      icon: Icons.tune_rounded,
      iconColor: const Color(0xFF6366F1),
      isDark: isDark,
      children: [
        _buildModernSettingsTile(
          title: provider.isDarkMode ? 'المظهر الداكن' : 'المظهر الفاتح',
          subtitle: provider.isDarkMode
              ? 'تفعيل المظهر الداكن للتطبيق'
              : 'تفعيل المظهر الفاتح للتطبيق',
          icon: provider.isDarkMode
              ? Icons.dark_mode_rounded
              : Icons.light_mode_rounded,
          iconColor: provider.isDarkMode
              ? const Color(0xFF8B8CF8)
              : const Color(0xFFF59E0B),
          isDark: isDark,
          trailing: Switch(
            value: provider.isDarkMode,
            onChanged: (value) async {
              await provider.toggleTheme();
            },
            activeColor: const Color(0xFF6366F1),
            inactiveThumbColor: isDark ? Colors.grey[600] : Colors.grey[400],
            inactiveTrackColor: isDark ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
        _buildModernSettingsTile(
          title: 'موقع شريط التنقل',
          subtitle: provider.isNavBarAtTop
              ? 'شريط التنقل في الأعلى'
              : 'شريط التنقل في الأسفل',
          icon: provider.isNavBarAtTop
              ? Icons.vertical_align_top_rounded
              : Icons.vertical_align_bottom_rounded,
          iconColor: const Color(0xFF10B981),
          isDark: isDark,
          trailing: Switch(
            value: provider.isNavBarAtTop,
            onChanged: (value) async {
              await provider.toggleNavBarPosition();
            },
            activeColor: const Color(0xFF10B981),
            inactiveThumbColor: isDark ? Colors.grey[600] : Colors.grey[400],
            inactiveTrackColor: isDark ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
      ],
    );
  }

  Widget _buildModernDataSettings(
    BuildContext context,
    AppProvider provider,
    bool isDark,
  ) {
    return _buildModernSettingsSection(
      title: 'إدارة البيانات',
      icon: Icons.storage_rounded,
      iconColor: const Color(0xFF3B82F6),
      isDark: isDark,
      children: [
        _buildModernSettingsTile(
          title: 'إحصائيات البيانات',
          subtitle:
              '${provider.totalStudents} طالب، ${provider.groups.length} مجموعة',
          icon: Icons.analytics_rounded,
          iconColor: const Color(0xFF3B82F6),
          isDark: isDark,
          trailing: Icon(
            Icons.arrow_forward_ios_rounded,
            color: isDark ? Colors.white54 : const Color(0xFF9CA3AF),
            size: 16,
          ),
          onTap: () {
            _showDataStatsDialog(context, provider);
          },
        ),
        _buildModernSettingsTile(
          title: 'مسح جميع البيانات',
          subtitle: 'حذف جميع الطلاب والمجموعات',
          icon: Icons.delete_forever_rounded,
          iconColor: const Color(0xFFEF4444),
          isDark: isDark,
          onTap: () {
            _showClearDataDialog(context, provider);
          },
        ),
        _buildModernSettingsTile(
          title: 'إعادة تعيين التطبيق',
          subtitle: 'إعادة التطبيق لحالته الأولى',
          icon: Icons.refresh_rounded,
          iconColor: const Color(0xFFF59E0B),
          isDark: isDark,
          onTap: () {
            _showResetAppDialog(context, provider);
          },
        ),
      ],
    );
  }

  Widget _buildModernBackupSettings(
    BuildContext context,
    AppProvider provider,
    bool isDark,
  ) {
    return _buildModernSettingsSection(
      title: 'النسخ الاحتياطي',
      icon: Icons.backup_rounded,
      iconColor: const Color(0xFF10B981),
      isDark: isDark,
      children: [
        _buildModernSettingsTile(
          title: 'إدارة النسخ الاحتياطية',
          subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
          icon: Icons.cloud_upload_rounded,
          iconColor: const Color(0xFF10B981),
          isDark: isDark,
          trailing: Icon(
            Icons.arrow_forward_ios_rounded,
            color: isDark ? Colors.white54 : const Color(0xFF9CA3AF),
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const BackupManagerScreen(),
              ),
            );
          },
        ),
        _buildModernSettingsTile(
          title: 'نسخة احتياطية سريعة',
          subtitle: 'إنشاء نسخة احتياطية فورية',
          icon: Icons.save_rounded,
          iconColor: const Color(0xFF8B5CF6),
          isDark: isDark,
          onTap: () async {
            await _createQuickBackup(context, provider);
          },
        ),
        _buildModernSettingsTile(
          title: 'النسخ التلقائي',
          subtitle: 'نسخ احتياطي تلقائي يومي',
          icon: Icons.schedule_rounded,
          iconColor: const Color(0xFFF59E0B),
          isDark: isDark,
          trailing: Switch(
            value: false, // يمكن ربطه بإعداد
            onChanged: (value) {
              // تنفيذ تفعيل النسخ التلقائي
            },
            activeColor: const Color(0xFF10B981),
            inactiveThumbColor: isDark ? Colors.grey[600] : Colors.grey[400],
            inactiveTrackColor: isDark ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
      ],
    );
  }

  Widget _buildModernAboutSettings(BuildContext context, bool isDark) {
    return _buildModernSettingsSection(
      title: 'حول التطبيق',
      icon: Icons.info_rounded,
      iconColor: const Color(0xFF8B5CF6),
      isDark: isDark,
      children: [
        _buildModernSettingsTile(
          title: 'إصدار التطبيق',
          subtitle: '1.0.0',
          icon: Icons.app_registration_rounded,
          iconColor: const Color(0xFF8B5CF6),
          isDark: isDark,
        ),
        _buildModernSettingsTile(
          title: 'فحص التحديثات',
          subtitle: 'التحقق من وجود إصدار جديد',
          icon: Icons.system_update_rounded,
          iconColor: const Color(0xFF06B6D4),
          isDark: isDark,
          trailing: Icon(
            Icons.arrow_forward_ios_rounded,
            color: isDark ? Colors.white54 : const Color(0xFF9CA3AF),
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CheckUpdatesScreen(),
              ),
            );
          },
        ),
        _buildModernSettingsTile(
          title: 'سياسة الخصوصية',
          subtitle: 'كيفية حماية بياناتك',
          icon: Icons.privacy_tip_rounded,
          iconColor: const Color(0xFFEC4899),
          isDark: isDark,
          trailing: Icon(
            Icons.arrow_forward_ios_rounded,
            color: isDark ? Colors.white54 : const Color(0xFF9CA3AF),
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PrivacyPolicyScreen(),
              ),
            );
          },
        ),
        _buildModernSettingsTile(
          title: 'المطور',
          subtitle: 'Mohamed Youssef',
          icon: Icons.code_rounded,
          iconColor: const Color(0xFF10B981),
          isDark: isDark,
        ),
      ],
    );
  }

  // دالة بناء قسم الإعدادات الحديث
  Widget _buildModernSettingsSection({
    required String title,
    required IconData icon,
    required Color iconColor,
    required bool isDark,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: isDark
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 15,
                  offset: const Offset(0, 3),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  blurRadius: 8,
                  offset: const Offset(0, 1),
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: iconColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : const Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  // دالة بناء عنصر الإعدادات الحديث
  Widget _buildModernSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
    required bool isDark,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF2A2A2A).withValues(alpha: 0.5)
            : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? Colors.white.withValues(alpha: 0.05)
              : const Color(0xFFE5E7EB),
          width: 0.5,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : const Color(0xFF1F2937),
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(
            fontSize: 13,
            color: isDark
                ? Colors.white.withValues(alpha: 0.7)
                : const Color(0xFF6B7280),
          ),
        ),
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }

  // عرض حوار إحصائيات البيانات
  void _showDataStatsDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1e293b),
                const Color(0xFF1e293b).withValues(alpha: 0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إحصائيات البيانات',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 20),
              _buildStatRow('إجمالي الطلاب', provider.totalStudents.toString()),
              _buildStatRow(
                'إجمالي المجموعات',
                provider.groups.length.toString(),
              ),
              _buildStatRow(
                'إجمالي الدروس',
                provider.lessons.length.toString(),
              ),
              _buildStatRow(
                'المواد المختلفة',
                provider.groups.map((g) => g.subject).toSet().length.toString(),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366f1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'إغلاق',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF6366f1),
            ),
          ),
        ],
      ),
    );
  }

  // عرض حوار مسح البيانات
  void _showClearDataDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1e293b),
        title: Text(
          'تأكيد مسح البيانات',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              // مسح جميع البيانات
              await DataService.students.clear();
              await DataService.groups.clear();
              await DataService.lessons.clear();
              provider.loadData();
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم مسح جميع البيانات بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('مسح', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // عرض حوار إعادة تعيين التطبيق
  void _showResetAppDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1e293b),
        title: Text(
          'إعادة تعيين التطبيق',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'سيتم إعادة التطبيق لحالته الأولى مع مسح جميع البيانات والإعدادات.',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              // مسح جميع البيانات
              await DataService.students.clear();
              await DataService.groups.clear();
              await DataService.lessons.clear();
              provider.loadData();
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم إعادة تعيين التطبيق بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }

  // إنشاء نسخة احتياطية سريعة
  Future<void> _createQuickBackup(
    BuildContext context,
    AppProvider provider,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      await BackupService.createBackup();

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            'تم إنشاء النسخة الاحتياطية بنجاح',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            'فشل في إنشاء النسخة الاحتياطية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

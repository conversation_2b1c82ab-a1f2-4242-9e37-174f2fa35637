import 'package:flutter/material.dart';

/// Simple & Beautiful Theme - نظام تصميم بسيط وجميل
class SimpleTheme {
  // Light Theme Colors - ألوان المظهر الفاتح
  static const Color lightBg = Color(0xFFFAFAFA);
  static const Color lightCardBg = Color(0xFFFFFFFF);
  static const Color lightSurfaceBg = Color(0xFFF5F5F5);
  static const Color lightSurfaceLight = Color(0xFFE0E0E0);

  // Dark Theme Colors - ألوان المظهر الداكن
  static const Color darkBg = Color(0xFF121212);
  static const Color cardBg = Color(0xFF1E1E1E);
  static const Color surfaceBg = Color(0xFF2A2A2A);
  static const Color surfaceLight = Color(0xFF3A3A3A);

  // Primary Colors - الألوان الأساسية
  static const Color primary = Color(0xFF6366F1);
  static const Color primaryLight = Color(0xFF8B8CF8);
  static const Color primaryDark = Color(0xFF4F46E5);
  static const Color secondary = Color(0xFF10B981);
  static const Color accent = Color(0xFFF59E0B);

  // Status Colors - ألوان الحالة
  static const Color success = Color(0xFF10B981);
  static const Color danger = Color(0xFFEF4444);
  static const Color warning = Color(0xFFF59E0B);
  static const Color info = Color(0xFF3B82F6);

  // Text Colors for Light Theme - ألوان النص للمظهر الفاتح
  static const Color lightTextPrimary = Color(0xFF1F2937);
  static const Color lightTextSecondary = Color(0xFF6B7280);
  static const Color lightTextMuted = Color(0xFF9CA3AF);
  static const Color lightTextDisabled = Color(0xFFD1D5DB);

  // Text Colors for Dark Theme - ألوان النص للمظهر الداكن
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB0B0B0);
  static const Color textMuted = Color(0xFF808080);
  static const Color textDisabled = Color(0xFF666666);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Additional colors for compatibility
  static const Color accentPink = Color(0xFFE91E63);
  static const Color accentGreen = Color(0xFF4CAF50);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color accentTeal = Color(0xFF009688);
  static const Color primaryBlue = primary; // Alias for compatibility
  static const Color primaryPurple = Color(0xFF9C27B0);

  // Light variants for compatibility
  static const Color primaryBlueDark = Color(0xFF1976D2);
  static const Color primaryPurpleLight = Color(0xFFBA68C8);
  static const Color accentPinkLight = Color(0xFFF48FB1);
  static const Color successLight = Color(0xFF81C784);
  static const Color warningLight = Color(0xFFFFB74D);

  // Simple Gradients - تدرجات بسيطة
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient lightCardGradient = LinearGradient(
    colors: [lightCardBg, lightSurfaceBg],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [cardBg, surfaceBg],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient lightBackgroundGradient = LinearGradient(
    colors: [lightBg, lightSurfaceBg],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [darkBg, cardBg],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient primaryGradientReverse = LinearGradient(
    colors: [primaryLight, primary],
    begin: Alignment.bottomRight,
    end: Alignment.topLeft,
  );

  // Simple Shadows - ظلال بسيطة وناعمة
  static const List<BoxShadow> lightCardShadow = [
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 10,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x05000000),
      blurRadius: 20,
      offset: Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> cardShadow = [
    BoxShadow(color: Color(0x1A000000), blurRadius: 8, offset: Offset(0, 2)),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(color: Color(0x26000000), blurRadius: 4, offset: Offset(0, 2)),
  ];

  // Simple Decorations - زخارف بسيطة
  static BoxDecoration get lightCardDecoration => BoxDecoration(
    color: lightCardBg,
    borderRadius: BorderRadius.circular(16),
    boxShadow: lightCardShadow,
    border: Border.all(color: lightSurfaceLight, width: 0.5),
  );

  static BoxDecoration get cardDecoration => BoxDecoration(
    gradient: cardGradient,
    borderRadius: BorderRadius.circular(12),
    boxShadow: cardShadow,
  );

  static BoxDecoration get buttonDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(12),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get premiumCard => BoxDecoration(
    gradient: cardGradient,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
    boxShadow: cardShadow,
  );

  static BoxDecoration get lightPremiumCard => BoxDecoration(
    color: lightCardBg,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: lightSurfaceLight, width: 0.5),
    boxShadow: lightCardShadow,
  );

  // Button decorations
  static BoxDecoration get primaryButton => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get secondaryButton => BoxDecoration(
    color: surfaceBg,
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
    boxShadow: cardShadow,
  );

  static BoxDecoration get dangerButton => BoxDecoration(
    color: danger,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get successButton => BoxDecoration(
    color: success,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  // Theme Data
  static ThemeData get darkTheme => ThemeData(
    brightness: Brightness.dark,
    primarySwatch: Colors.blue,
    primaryColor: primary,
    scaffoldBackgroundColor: darkBg,
    cardColor: cardBg,
    dividerColor: surfaceBg,

    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: textPrimary),
      titleTextStyle: TextStyle(
        color: textPrimary,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      color: cardBg,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: textPrimary,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),

    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cardBg,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      contentPadding: const EdgeInsets.all(16),
    ),

    colorScheme: const ColorScheme.dark(
      primary: primary,
      secondary: secondary,
      surface: cardBg,
      error: danger,
    ),
  );

  static ThemeData get lightTheme => ThemeData(
    brightness: Brightness.light,
    primarySwatch: Colors.indigo,
    primaryColor: primary,
    scaffoldBackgroundColor: lightBg,
    cardColor: lightCardBg,
    dividerColor: lightSurfaceLight,

    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: lightTextPrimary),
      titleTextStyle: TextStyle(
        color: lightTextPrimary,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      color: lightCardBg,
      elevation: 0,
      shadowColor: Colors.black.withValues(alpha: 0.05),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: Colors.white,
        elevation: 2,
        shadowColor: primary.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),

    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: lightSurfaceBg,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: lightSurfaceLight),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: lightSurfaceLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primary, width: 2),
      ),
      contentPadding: const EdgeInsets.all(16),
    ),

    colorScheme: const ColorScheme.light(
      primary: primary,
      secondary: secondary,
      surface: lightCardBg,
      error: danger,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: lightTextPrimary,
      onError: Colors.white,
    ),
  );
}

/// Performance-optimized animations - رسوم متحركة محسنة للأداء
class SimpleAnimations {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 250);
  static const Duration slow = Duration(milliseconds: 400);

  static const Curve easeOut = Curves.easeOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve bounce = Curves.elasticOut;

  // Simple fade animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = normal,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: easeOut,
      builder: (context, value, child) {
        return Opacity(opacity: value, child: child);
      },
      child: child,
    );
  }

  // Simple scale animation
  static Widget scaleIn({
    required Widget child,
    Duration duration = normal,
    double begin = 0.8,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: bounce,
      builder: (context, value, child) {
        return Transform.scale(scale: value, child: child);
      },
      child: child,
    );
  }

  // Simple slide animation
  static Widget slideIn({
    required Widget child,
    Duration duration = normal,
    Offset begin = const Offset(0, 0.2),
    Offset end = Offset.zero,
  }) {
    return TweenAnimationBuilder<Offset>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: easeOut,
      builder: (context, value, child) {
        return Transform.translate(offset: value, child: child);
      },
      child: child,
    );
  }
}

/// Performance utilities - أدوات تحسين الأداء
class PerformanceHelper {
  static bool isLowEndDevice() {
    // Simple check for low-end devices
    return false; // يمكن تحسينه لاحقاً
  }

  static Duration getAnimationDuration() {
    return isLowEndDevice() ? SimpleAnimations.fast : SimpleAnimations.normal;
  }

  static Widget optimizedBuilder({
    required Widget Function() builder,
    Widget? fallback,
  }) {
    try {
      return builder();
    } catch (e) {
      return fallback ?? const SizedBox.shrink();
    }
  }
}

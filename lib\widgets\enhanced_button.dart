import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';

enum ButtonType { primary, secondary, danger, success, ghost, outline }

enum ButtonSize { small, medium, large }

class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final IconData? icon;
  final bool iconRight;
  final bool loading;
  final bool disabled;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final LinearGradient? customGradient;
  final Color? customTextColor;
  final Duration animationDuration;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.iconRight = false,
    this.loading = false,
    this.disabled = false,
    this.margin,
    this.width,
    this.customGradient,
    this.customTextColor,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    try {
      _controller = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      _scaleAnimation = Tween<double>(
        begin: 1.0,
        end: 0.95,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

      _opacityAnimation = Tween<double>(
        begin: 1.0,
        end: 0.8,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    } catch (e) {
      // Handle initialization error
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    try {
      _controller.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    super.dispose();
  }

  BoxDecoration _getDecoration() {
    if (widget.disabled) {
      return BoxDecoration(
        color: SimpleTheme.textMuted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
      );
    }

    switch (widget.type) {
      case ButtonType.primary:
        return SimpleTheme.primaryButton;
      case ButtonType.secondary:
        return SimpleTheme.secondaryButton;
      case ButtonType.danger:
        return SimpleTheme.dangerButton;
      case ButtonType.success:
        return SimpleTheme.successButton;
      case ButtonType.ghost:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        );
      case ButtonType.outline:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(color: SimpleTheme.primary, width: 1.5),
        );
    }
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case ButtonSize.small:
        return 12.0;
      case ButtonSize.medium:
        return 16.0;
      case ButtonSize.large:
        return 20.0;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 14.0;
      case ButtonSize.medium:
        return 16.0;
      case ButtonSize.large:
        return 18.0;
    }
  }

  Color _getTextColor() {
    if (widget.customTextColor != null) {
      return widget.customTextColor!;
    }

    if (widget.disabled) {
      return SimpleTheme.textDisabled;
    }

    switch (widget.type) {
      case ButtonType.primary:
      case ButtonType.danger:
      case ButtonType.success:
        return SimpleTheme.textOnPrimary;
      case ButtonType.secondary:
        return SimpleTheme.textPrimary;
      case ButtonType.ghost:
      case ButtonType.outline:
        return SimpleTheme.primary;
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.disabled || widget.loading || _isDisposed || !mounted) return;
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.disabled || widget.loading || _isDisposed || !mounted) return;
    _controller.reverse();
  }

  void _handleTapCancel() {
    if (widget.disabled || widget.loading || _isDisposed || !mounted) return;
    _controller.reverse();
  }

  Widget _buildContent() {
    final textStyle = TextStyle(
      fontSize: _getFontSize(),
      fontWeight: FontWeight.w600,
      color: _getTextColor(),
    );

    if (widget.loading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: _getFontSize(),
            height: _getFontSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
            ),
          ),
          const SizedBox(width: 8),
          Text('جاري التحميل...', style: textStyle),
        ],
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: widget.iconRight
            ? [
                Text(widget.text, style: textStyle),
                const SizedBox(width: 8),
                Icon(
                  widget.icon,
                  size: _getFontSize() + 2,
                  color: _getTextColor(),
                ),
              ]
            : [
                Icon(
                  widget.icon,
                  size: _getFontSize() + 2,
                  color: _getTextColor(),
                ),
                const SizedBox(width: 8),
                Text(widget.text, style: textStyle),
              ],
      );
    }

    return Text(widget.text, style: textStyle);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      width: widget.width,
      child: RepaintBoundary(
        child: AnimatedBuilder(
          animation: Listenable.merge([_scaleAnimation, _opacityAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: _opacityAnimation.value,
                child: Container(
                  decoration: widget.customGradient != null
                      ? _getDecoration().copyWith(
                          gradient: widget.customGradient,
                        )
                      : _getDecoration(),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: widget.disabled || widget.loading
                          ? null
                          : widget.onPressed,
                      onTapDown: _handleTapDown,
                      onTapUp: _handleTapUp,
                      onTapCancel: _handleTapCancel,
                      borderRadius: BorderRadius.circular(_getBorderRadius()),
                      splashColor: Colors.white.withValues(alpha: 0.2),
                      highlightColor: Colors.white.withValues(alpha: 0.1),
                      child: Container(
                        padding: _getPadding(),
                        child: Center(child: _buildContent()),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../widgets/connection_indicator.dart';
import '../providers/app_provider.dart';
import '../widgets/enhanced_background.dart';
import '../theme/simple_theme.dart';
import '../widgets/smooth_animations.dart';
import 'home_screen.dart';
import 'schedule_screen.dart';
import 'groups_screen.dart';
import 'settings_screen.dart';
import 'attendance_table_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;

  final List<Widget> _screens = [
    const HomeScreen(),
    const ScheduleScreen(),
    const GroupsScreen(),
    const AttendanceTableScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: Scaffold(
            body: EnhancedBackground(
              type: BackgroundType.particles,
              enableAnimation: true,
              intensity: 0.08,
              child: SafeArea(
                child: Column(
                  children: [
                    // Modern top bar with animation
                    SmoothAnimations.smoothEntry(
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: SimpleTheme.cardGradient,
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.1),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                              spreadRadius: -4,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            const ConnectionIndicator(),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                gradient: SimpleTheme.primaryGradient,
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: SimpleTheme.primaryBlue.withValues(
                                      alpha: 0.4,
                                    ),
                                    blurRadius: 16,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: Image.asset(
                                      'lib/logo/icon.png',
                                      width: 20,
                                      height: 20,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                            return const Icon(
                                              Icons.school_rounded,
                                              color: Colors.white,
                                              size: 20,
                                            );
                                          },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'EduTrack',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Main content
                    Expanded(
                      child: Stack(
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            transitionBuilder: (child, animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: SlideTransition(
                                  position:
                                      Tween<Offset>(
                                        begin: const Offset(0.1, 0),
                                        end: Offset.zero,
                                      ).animate(
                                        CurvedAnimation(
                                          parent: animation,
                                          curve: Curves.easeInOut,
                                        ),
                                      ),
                                  child: child,
                                ),
                              );
                            },
                            child: _screens[_currentIndex],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: provider.isNavBarAtTop
                ? null
                : _buildModernBottomNavBar(),
            appBar: provider.isNavBarAtTop ? _buildTopNavBar() : null,
          ),
        );
      },
    );
  }

  // بناء شريط التنقل العلوي
  PreferredSizeWidget _buildTopNavBar() {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final isDark = provider.isDarkMode;
        return PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: AppBar(
          backgroundColor: isDark
              ? const Color(0xFF121212)
              : const Color(0xFFFAFAFA),
          elevation: 0,
          automaticallyImplyLeading: false,
          title: Container(
            height: 60,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isDark
                    ? Colors.white.withValues(alpha: 0.1)
                    : const Color(0xFFE5E7EB),
                width: 0.5,
              ),
              boxShadow: isDark
                  ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 15,
                        offset: const Offset(0, 3),
                      ),
                    ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildModernNavItem(
                    0,
                    Icons.home_rounded,
                    'الرئيسية',
                    isDark,
                  ),
                  _buildModernNavItem(
                    1,
                    Icons.calendar_today_rounded,
                    'الجدول',
                    isDark,
                  ),
                  _buildModernNavItem(
                    2,
                    Icons.groups_rounded,
                    'المجموعات',
                    isDark,
                  ),
                  _buildModernNavItem(
                    3,
                    Icons.table_chart_rounded,
                    'الحضور',
                    isDark,
                  ),
                  _buildModernNavItem(
                    4,
                    Icons.settings_rounded,
                    'الإعدادات',
                    isDark,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء شريط التنقل الحديث
  Widget _buildModernBottomNavBar() {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final isDark = provider.isDarkMode;
        return Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 20),
          height: 70,
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isDark
                  ? Colors.white.withValues(alpha: 0.1)
                  : const Color(0xFFE5E7EB),
              width: 0.5,
            ),
            boxShadow: isDark
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.03),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: SafeArea(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildModernNavItem(
                    0,
                    Icons.home_rounded,
                    'الرئيسية',
                    isDark,
                  ),
                  _buildModernNavItem(
                    1,
                    Icons.calendar_today_rounded,
                    'الجدول',
                    isDark,
                  ),
                  _buildModernNavItem(
                    2,
                    Icons.groups_rounded,
                    'المجموعات',
                    isDark,
                  ),
                  _buildModernNavItem(
                    3,
                    Icons.table_chart_rounded,
                    'الحضور',
                    isDark,
                  ),
                  _buildModernNavItem(
                    4,
                    Icons.settings_rounded,
                    'الإعدادات',
                    isDark,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء عنصر التنقل الحديث
  Widget _buildModernNavItem(
    int index,
    IconData icon,
    String label,
    bool isDark,
  ) {
    final isSelected = _currentIndex == index;
    final primaryColor = const Color(0xFF6366F1);
    final mutedColor = isDark
        ? const Color(0xFF9CA3AF)
        : const Color(0xFF6B7280);

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            HapticFeedback.lightImpact();
            setState(() => _currentIndex = index);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة حديثة مع خلفية
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 22,
                    color: isSelected ? primaryColor : mutedColor,
                  ),
                ),
                const SizedBox(height: 4),
                // نص حديث
                Text(
                  label,
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected ? primaryColor : mutedColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

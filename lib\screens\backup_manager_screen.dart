import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../services/backup_service.dart';
import '../theme/simple_theme.dart';

class BackupManagerScreen extends StatefulWidget {
  const BackupManagerScreen({super.key});

  @override
  State<BackupManagerScreen> createState() => _BackupManagerScreenState();
}

class _BackupManagerScreenState extends State<BackupManagerScreen> {
  List<FileSystemEntity> _backups = [];
  bool _isLoading = true;
  String _backupPath = '';

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final directory = await BackupService.getBackupDirectory();
      _backupPath = directory.path;
      final backups = await BackupService.getAvailableBackups();

      if (!mounted) return;

      setState(() {
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل تحميل النسخ الاحتياطية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final backupPath = await BackupService.createBackup();

      if (!mounted) return;

      if (backupPath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء النسخة الاحتياطية بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: SimpleTheme.success,
          ),
        );
        _loadBackups();
      } else {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل إنشاء النسخة الاحتياطية',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل إنشاء النسخة الاحتياطية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _restoreBackup(File file) async {
    // تأكيد استعادة النسخة الاحتياطية
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'تأكيد استعادة البيانات',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هل تريد المتابعة؟',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              _performRestore(file);
            },
            child: Text(
              'استعادة',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _performRestore(File file) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await BackupService.restoreBackupFromFile(file);

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (success) {
        final provider = Provider.of<AppProvider>(context, listen: false);
        provider.loadData();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم استعادة البيانات بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: SimpleTheme.success,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل استعادة البيانات', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل استعادة البيانات: $e', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteBackup(FileSystemEntity file) async {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'حذف النسخة الاحتياطية',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              _performDelete(file);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _performDelete(FileSystemEntity file) async {
    try {
      await file.delete();

      if (!mounted) return;

      _loadBackups();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حذف النسخة الاحتياطية بنجاح',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: SimpleTheme.success,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل حذف النسخة الاحتياطية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة النسخ الاحتياطية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: SimpleTheme.cardBg,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [SimpleTheme.darkBg, SimpleTheme.cardBg],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مجلد النسخ الاحتياطية:',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black26,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _backupPath,
                            style: GoogleFonts.cairo(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'النسخ الاحتياطية المتوفرة:',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.refresh),
                          label: Text('تحديث', style: GoogleFonts.cairo()),
                          onPressed: _loadBackups,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: SimpleTheme.primary,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: _backups.isEmpty
                        ? Center(
                            child: Text(
                              'لا توجد نسخ احتياطية متوفرة',
                              style: GoogleFonts.cairo(
                                color: Colors.white70,
                                fontSize: 16,
                              ),
                            ),
                          )
                        : ListView.builder(
                            itemCount: _backups.length,
                            itemBuilder: (context, index) {
                              final file = _backups[index];
                              final fileName = file.path.split('/').last;

                              // استخراج التاريخ والوقت من اسم الملف
                              String dateTime = '';
                              final regex = RegExp(
                                r'edutrack_backup_(\d{8})_(\d{6})',
                              );
                              final match = regex.firstMatch(fileName);
                              if (match != null) {
                                final date = match.group(1)!;
                                final time = match.group(2)!;
                                dateTime =
                                    '${date.substring(6, 8)}/${date.substring(4, 6)}/${date.substring(0, 4)} ${time.substring(0, 2)}:${time.substring(2, 4)}:${time.substring(4, 6)}';
                              }

                              return Card(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 4,
                                ),
                                color: Colors.black12,
                                child: ListTile(
                                  title: Text(
                                    dateTime.isNotEmpty ? dateTime : fileName,
                                    style: GoogleFonts.cairo(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  subtitle: Text(
                                    fileName,
                                    style: GoogleFonts.cairo(
                                      color: Colors.white70,
                                      fontSize: 12,
                                    ),
                                  ),
                                  leading: const Icon(
                                    Icons.backup,
                                    color: Color(0xFF6366f1),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(
                                          Icons.restore,
                                          color: Color(0xFF6366f1),
                                        ),
                                        onPressed: () =>
                                            _restoreBackup(file as File),
                                        tooltip: 'استعادة',
                                      ),
                                      IconButton(
                                        icon: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                        ),
                                        onPressed: () => _deleteBackup(file),
                                        tooltip: 'حذف',
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createBackup,
        backgroundColor: SimpleTheme.primary,
        child: const Icon(Icons.add),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';

import '../widgets/stats_dashboard.dart';
import '../widgets/smooth_animations.dart';
import '../widgets/interactive_widgets.dart';
import '../theme/simple_theme.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final isDark = provider.isDarkMode;
        return Container(
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF121212) : const Color(0xFFFAFAFA),
          ),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modern Header
                SmoothAnimations.smoothEntry(
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: isDark
                        ? [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 20,
                              offset: const Offset(0, 4),
                            ),
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.03),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                    ),
                    child: Row(
                      children: [
                        // Modern Logo Container
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF6366F1), Color(0xFF8B8CF8)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.asset(
                              'lib/logo/icon.png',
                              width: 28,
                              height: 28,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  gradient: SimpleTheme.primaryGradientReverse,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.dashboard_rounded,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Modern Welcome Text
                            Text(
                              'مرحباً بك في',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: isDark
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : const Color(0xFF6B7280),
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Modern Title
                            Text(
                              'EduTrack',
                              style: GoogleFonts.cairo(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: isDark
                                  ? Colors.white
                                  : const Color(0xFF1F2937),
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(height: 2),
                            // Modern Subtitle
                            Text(
                              'نظام إدارة التعليم المتقدم',
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                color: isDark
                                  ? Colors.white.withValues(alpha: 0.7)
                                  : const Color(0xFF6B7280),
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  ),
                ),
                const SizedBox(height: 32),

                // Enhanced Stats Dashboard
                QuickStats(
                  totalGroups: provider.totalGroups,
                  totalStudents: provider.totalStudents,
                  completedLessons: provider.completedLessonsToday,
                  remainingLessons: provider.remainingLessonsToday,
                  onGroupsTap: () => _showGroupsModal(context, provider),
                  onStudentsTap: () => _showStudentsModal(context, provider),
                  onCompletedTap: () =>
                      _showCompletedLessonsModal(context, provider),
                  onRemainingTap: () =>
                      _showRemainingLessonsModal(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showGroupsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'المجموعات',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.groups.length,
            itemBuilder: (context, index) {
              final group = provider.groups[index];
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  group.subject,
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(Icons.groups_rounded, color: SimpleTheme.primary),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showStudentsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الطلاب',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.students.length,
            itemBuilder: (context, index) {
              final student = provider.students[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == student.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  student.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.person_rounded,
                  color: SimpleTheme.accentPink,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showCompletedLessonsModal(BuildContext context, AppProvider provider) {
    final completedLessons = provider
        .getTodayLessons()
        .where((l) => l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الدروس المكتملة اليوم',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: completedLessons.length,
            itemBuilder: (context, index) {
              final lesson = completedLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.check_circle_rounded,
                  color: SimpleTheme.success,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemainingLessonsModal(BuildContext context, AppProvider provider) {
    final remainingLessons = provider
        .getTodayLessons()
        .where((l) => !l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الدروس المتبقية اليوم',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: remainingLessons.length,
            itemBuilder: (context, index) {
              final lesson = remainingLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.schedule_rounded,
                  color: SimpleTheme.warning,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}
